---
type: "always_apply"
description: "Example description"
---
# 插件开发指南


## 🔄 插件运行流程

### 1. 插件加载机制

#### 1.1 项目结构
```
virtual-power-plant-web/
├── plugins/                    # 插件目录
│   └── vpp-resource-manager/   # 具体插件
│       ├── public/
│       │   └── config.js       # 插件配置文件
│       ├── src/
│       │   ├── main.js         # 插件入口文件
│       │   ├── omega/          # Omega框架配置
│       │   ├── router/         # 路由配置
│       │   ├── projects/       # 业务模块
│       │   └── resources/      # 样式资源
│       └── package.json
├── Fusion-template/            # 主应用模板
│   ├── main/                   # 主应用
│   └── sub/                    # 子应用
└── package.json               # 根配置
```

#### 1.2 插件启动流程
1. **环境准备**
   ```bash
   # 安装依赖
   pnpm install
   
   # 启动插件
   pnpm dev:vpp
   ```

2. **插件初始化**
   - 加载 `src/main.js` 入口文件
   - 注册 Omega 插件系统
   - 初始化路由、状态管理、主题等

3. **配置加载**
   - 读取 `public/config.js` 配置文件
   - 注册菜单、权限、国际化等配置

4. **应用启动**
   - 创建 Vue 应用实例
   - 挂载到 DOM 节点

### 2. 插件与主应用集成

#### 2.1 微前端集成
```javascript
// Fusion-template/main/src/omega/fusion.js
const develop = {
  appList: [
    {
      name: "vpp-resource-manager",
      url: "//localhost:9528"
    }
  ],
  options: {
    systemConfig: "local",
    isMultiProject: true
  }
};
```

#### 2.2 路由集成
插件路由会自动集成到主应用的路由系统中，支持：
- 独立路由管理
- 路由懒加载
- 路由守卫
- 缓存控制

## 🛠️ 插件开发规范

### 1. 目录结构规范

```
src/
├── api/                    # API 接口
│   └── custom.js
├── config/                 # 配置文件
│   ├── enums.js           # 枚举定义
│   └── lang/              # 国际化
├── hooks/                  # 自定义 Hooks
├── icons/                  # SVG 图标
├── omega/                  # Omega 框架配置
│   ├── index.js           # 插件注册入口
│   ├── http.js            # HTTP 配置
│   ├── i18n.js            # 国际化配置
│   ├── theme.js           # 主题配置
│   └── subApp.js          # 子应用配置
├── projects/               # 业务模块
│   └── [module-name]/     # 具体业务模块
├── resources/              # 样式资源
│   ├── index.css
│   ├── index.scss
│   ├── var.scss           # SCSS 变量
│   └── elementui/         # ElementUI 定制样式
├── router/                 # 路由配置
│   ├── index.js
│   └── main.vue
├── store/                  # 状态管理
└── main.js                # 入口文件
```

### 2. 插件入口文件规范

```javascript
// src/main.js
import omegaApp from "@omega/app";
import "./omega/index";
import "./resources/index.css";
import "./resources/index.scss";
import "./icons/index.js";

import Vue from "vue";
import ElementUI from "element-ui";
import routerOption from "./router";
import storeOption from "./store";

// 插件配置
Vue.use(ElementUI, { size: "small" });

// 启动应用
omegaApp.createApp({
  el: "#app",
  config: {},
  routerOption,
  storeOption
});
```

### 3. Omega 插件注册规范

```javascript
// src/omega/index.js
import "./i18n.js";
import "./theme.js";
import "./http.js";
import "./subApp.js";

import Vue from "vue";
import OmegaIcon from "@omega/icon";

Vue.use(OmegaIcon);
```

### 4. 路由配置规范

```javascript
// src/router/index.js
import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        path: "",
        redirect: "/default-page"
      },
      {
        meta: {
          keepAlive: true  // 页面缓存
        },
        path: "/module-page",
        component: () => import("@/projects/module/index.vue")
      }
    ]
  }
];

export default {
  routes: appRoutes
};
```

## 🎨 样式开发规范

### 1. 样式架构

#### 1.1 样式文件组织
```
resources/
├── index.css              # 全局样式入口
├── index.scss             # SCSS 样式入口
├── var.scss               # SCSS 变量文件
├── _theme.extend.scss     # 主题扩展
└── elementui/
    └── elementui-custom.scss  # ElementUI 定制
```

#### 1.2 样式导入顺序
```scss
// resources/index.scss
@import "~@omega/theme/index.scss";     // Omega 主题
@import "./elementui/elementui-custom.scss";  // ElementUI 定制
```

### 2. 主题系统规范

#### 2.1 主题变量定义
```scss
// resources/_theme.extend.scss
$omega_theme_extend: (
  "light": (
    LOGIN_BG: linear-gradient(90deg, rgba(231, 235, 246, 1) 0%, rgba(231, 251, 242, 1) 100%),
    LOGIN_LOGO_IMG: url("/static/image/login_logo_light.png")
  ),
  "dark": (
    LOGIN_BG: linear-gradient(rgb(3, 152, 216) 0%, rgb(99, 131, 255) 0%, rgb(34, 46, 102) 100%),
    LOGIN_LOGO_IMG: url("/static/image/login_logo_dark.png")
  )
);
```

#### 2.2 CSS 变量使用
```css
.page-container {
  background-color: var(--BG1);    /* 背景色 */
  color: var(--T1);                /* 主文本色 */
  font-size: var(--H3);            /* 标题字号 */
  padding: var(--J2);              /* 间距 */
}
```

### 3. TailwindCSS 集成规范

#### 3.1 配置文件
```javascript
// tailwind.config.js
module.exports = {
  darkMode: "class",
  content: ["./src/projects/**/*.{vue,html,js,md}"],
  theme: {
    extend: {
      colors: {
        ZS: "var(--ZS)",      // 主色
        F1: "var(--F1)",      // 辅助色1
        F2: "var(--F2)",      // 辅助色2
        Sta1: "var(--Sta1)",  // 成功色
        Sta2: "var(--Sta2)",  // 警告色
        Sta3: "var(--Sta3)",  // 危险色
      }
    }
  }
};
```

#### 3.2 使用规范
```vue
<template>
  <div class="bg-ZS text-T1 p-J2 rounded-lg">
    <h3 class="text-H3 font-semibold mb-J1">标题</h3>
    <p class="text-T2">内容文本</p>
  </div>
</template>
```

### 4. ElementUI 定制规范

#### 4.1 组件样式定制
```scss
// resources/elementui/elementui-custom.scss
.el-dialog {
  border-radius: 12px;
  
  .el-dialog__title {
    font-weight: 600;
    line-height: 22px;
    font-size: var(--H3);
  }
  
  .el-dialog__body {
    background-color: var(--BG);
    padding: 8px;
    
    .dialog-content {
      border-radius: 8px;
      background-color: var(--BG1);
      padding: var(--J2);
    }
  }
}
```

#### 4.2 响应式适配
```scss
// 子应用状态下的样式适配
body {
  position: relative !important;
}

.el-popper,
.el-tooltip__popper {
  position: absolute !important;
}
```

### 5. 样式开发注意事项

#### 5.1 必须遵循的规范
1. **使用 CSS 变量**：所有颜色、字号、间距必须使用预定义的 CSS 变量
2. **支持主题切换**：样式必须兼容明暗主题
3. **响应式设计**：考虑不同屏幕尺寸的适配
4. **组件隔离**：使用 scoped 样式避免全局污染

#### 5.2 禁止的做法
1. ❌ 硬编码颜色值：`color: #333333`
2. ❌ 硬编码尺寸：`font-size: 14px`
3. ❌ 全局样式污染：不使用 scoped
4. ❌ 内联样式：`style="color: red"`

#### 5.3 推荐的做法
1. ✅ 使用 CSS 变量：`color: var(--T1)`
2. ✅ 使用预定义尺寸：`font-size: var(--H3)`
3. ✅ 使用 scoped 样式：`<style lang="scss" scoped>`
4. ✅ 使用 TailwindCSS 类：`class="bg-ZS text-T1"`


## 🚀 最佳实践

### 1. 开发流程
1. **创建插件目录**：在 `plugins/` 下创建新插件
2. **复制模板结构**：参考现有插件的目录结构
3. **配置插件信息**：修改 `package.json` 和 `config.js`
4. **开发业务功能**：在 `projects/` 下创建业务模块
5. **测试集成**：在主应用中测试插件功能


### A. 常用 CSS 变量参考

#### A.1 颜色变量
```css
/* 主色系 */
--ZS: 主色
--F1: 辅助色1
--F2: 辅助色2

/* 文本色 */
--T1: 主文本色
--T2: 次要文本色
--T3: 辅助文本色

/* 背景色 */
--BG: 主背景色
--BG1: 次要背景色
--BG2: 辅助背景色

/* 状态色 */
--Sta1: 成功色
--Sta2: 警告色
--Sta3: 危险色
```

#### A.2 尺寸变量
```css
/* 字号 */
--H1: 大标题
--H2: 中标题
--H3: 小标题
--B1: 正文大
--B2: 正文中
--B3: 正文小

/* 间距 */
--J1: 小间距
--J2: 中间距
--J3: 大间距
```
**维护者**: 虚拟电厂平台开发团队
