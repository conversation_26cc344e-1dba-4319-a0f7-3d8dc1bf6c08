/**
 * @fileoverview http://eslint.org/docs/user-guide/configuring
 */
{
  "root": true,
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "parser": "@babel/eslint-parser",
    "sourceType": "module",
    "requireConfigFile": false
  },
  "env": {
    "browser": true,
    "jquery": true,
    "commonjs": true,
    "node": true,
    "es2021": true
  },
  "globals": {
    "Vue": "readonly",
    "$T": "readonly",
    "mhjs": "readonly",
    "_": "readonly"
  },
  // https://eslint.vuejs.org/rules/#priority-b-strongly-recommended-improving-readability-for-vue-js-2-x
  // https://eslint.bootcss.com/docs/rules/
  "extends": ["plugin:vue/essential", "eslint:recommended", "prettier"],
  // add your custom rules here
  // "off" 或 0 - 关闭规则
  // "warn" 或 1 - 开启规则，使用警告级别的错误：warn (不会导致程序退出)
  // "error" 或 2 - 开启规则，使用错误级别的错误：error (当被触发的时候，程序会退出)
  "rules": {
    // 关闭结尾分号规则
    "semi": 0,
    // 关闭括号规则
    "quotes": 0,
    // 要求或禁止函数圆括号之前有一个空格
    "space-before-function-paren": 0,
    // 要求箭头函数的参数使用圆括号
    "arrow-parens": 0,
    // allow async-await
    "generator-star-spacing": 0,
    // debugger 进行警告提醒
    "no-debugger": 1,
    // 要求或禁止在注释前有空白
    "spaced-comment": 0,
    // 允许重复导入
    "import/no-duplicates": "off",
    // 关闭vue对prop字段短横线校验
    "vue/attribute-hyphenation": [
      "off",
      "never",
      {
        "ignore": []
      }
    ],
    // 已定义, 未使用的变量规则降级为警告.
    "no-unused-vars": [
      "warn",
      { "vars": "all", "args": "after-used", "ignoreRestSiblings": false }
    ]
  }
}
