// 适配子应用状态下，element-ui 弹框定位问题
body {
  position: relative !important;
}

.el-popper,
.el-tooltip__popper {
  position: absolute !important;
}

.el-dialog {
  border-radius: 12px;

  .el-dialog__title {
    font-weight: 600;
    line-height: 22px;
    font-size: var(--H3);
  }

  .el-dialog__body {
    background-color: var(--BG);
    padding: 8px;
    line-height: normal;

    .dialog-content {
      border-radius: 8px;
      background-color: var(--BG1);
      padding: var(--J2);
    }
  }

  .el-dialog__header {
    padding: var(--J2) var(--J3);
  }
}


.el-drawer__header {
  span {
    // font-weight: 600;
    line-height: 22px;
    font-size: var(--H3);
    color: var(--T1);
  }

  padding: var(--J2);
  margin-bottom: 0;
  // font-weight: 600;
  // line-height: 22px;
  // font-size: var(--H3);
}

.el-drawer__body {
  background-color: var(--BG);
  padding: 8px;
  line-height: normal;

  .dialog-content {
    border-radius: 8px;
    background-color: var(--BG1);
    padding: var(--J2);
  }
}