# 虚拟电厂平台项目结构与工作原理说明

## 1. 顶层结构说明

本项目采用 Monorepo 管理方式，根目录下包含多个子项目和配置文件，主要结构如下：

- `plugins/`：主业务插件目录，包含各类功能模块。
- `Fusion-template/`：前端模板工程，包含主应用（main）和子应用（sub），可作为微前端或多项目开发的基础。
- `package.json`、`pnpm-workspace.yaml`：工作区和依赖管理配置。
- 其他如 `.gitignore`、`.npmrc`、`.prettierrc.json` 等为通用配置文件。

## 2. Fusion-template 结构与用途

`Fusion-template` 目录下为前端模板工程，采用 Vue2 + ElementUI + TailwindCSS 技术栈，支持微前端架构。其结构如下：

- `main/`：主应用，负责整体框架、路由、权限、主题等核心功能。
- `sub/`：子应用，适合拆分为独立业务模块或微前端子项目。
- 每个应用下均有 `src/` 目录，包含 `api/`、`config/`、`router/`、`store/`、`projects/`（业务模块）、`omega/`（工具库）、`resources/`（样式与资源）等。

## 3. main 与 sub 的区别和联系

- `main` 作为主应用，负责整体页面框架、全局状态管理、主题切换、权限校验等。
- `sub` 作为子应用，结构与 main 类似，适合开发独立业务模块，可单独运行或集成到主应用。
- 二者均可独立开发、调试，方便多人协作和模块解耦。

## 4. 启动与开发流程

以 main 应用为例：

1. 安装依赖：
   ```sh
   npm install
   ```
2. 启动开发环境：
   ```sh
   npm run dev
   ```
3. 跳过登录（开发调试）：
   - 推荐：在 `main/.env.local` 文件中设置 `omegaCliDevserver.mockAuth=true`，可模拟免登录。
   - 或在 `src/omega/auth.js` 的 `whiteRouteList` 添加白名单路由。

sub 应用启动方式与 main 类似。

## 5. 依赖与环境变量说明

- 依赖管理采用 npm workspace，主依赖见各自 `package.json`。
- 主要依赖：Vue2、ElementUI、TailwindCSS、omega 系列工具库等。
- 环境变量通过 `.env` 或 `.env.local` 配置，支持 mock 登录、代理等功能。
- 本地代理可在 `var/` 目录下自定义，便于多环境开发。

## 6. 其他说明

- 详细开发、构建、测试流程请参考各子项目下的 `README.md`。
- 推荐使用 Node 18.x 版本。
- 如需扩展业务模块，可在 `projects/` 目录下新增业务子模块。

---

如有疑问或需进一步了解，请查阅各目录下的 README 或联系项目维护者。 