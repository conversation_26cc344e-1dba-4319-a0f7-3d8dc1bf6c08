<template>
  <el-table :data="tableData" border style="width: 100%">
    <el-table-column
      fixed
      prop="date"
      label="日期"
      width="150"
    ></el-table-column>
    <el-table-column prop="name" label="姓名" width="120"></el-table-column>
    <el-table-column prop="province" label="省份" width="120"></el-table-column>
    <el-table-column prop="city" label="市区" width="120"></el-table-column>
    <el-table-column prop="address" label="地址" width="300"></el-table-column>
    <el-table-column prop="zip" label="邮编" width="120"></el-table-column>
    <el-table-column fixed="right" label="操作" width="100">
      <template slot-scope="scope">
        <el-button @click="handleClick(scope.row)" type="text" size="small">
          查看
        </el-button>
        <el-button type="text" size="small" @click="handleClick($event)">
          编辑
          <OmegaIcon
            symbolId="collect-lin"
            size="middle"
            @click="evCollectClick"
          ></OmegaIcon>
        </el-button>
        <!-- 
        <OmegaIcon symbolId="pop-ups-lin"></OmegaIcon>
        <OmegaIcon symbolId="error-lin"></OmegaIcon> -->
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import OmegaIcon from "@omega/icon";
export default {
  name: "gridDemo",
  components: {
    OmegaIcon
  },
  methods: {
    handleClick(event) {},
    evCollectClick() {
      console.log("xxxxxxxxxxxx");
    }
  },

  data() {
    let tableData = [];
    for (let i = 0; i < 50; i++) {
      tableData.push({
        date: "2016-05-02",
        name: "王小虎",
        province: "上海",
        city: "普陀区",
        address: "上海市普陀区金沙江路 1518 弄",
        zip: i
      });
    }
    return { tableData };
  }
};
</script>
