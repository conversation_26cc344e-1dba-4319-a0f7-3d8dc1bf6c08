# 虚拟电厂平台样式开发规范

## 📋 目录
- [样式架构概述](#样式架构概述)
- [CSS变量系统](#css变量系统)
- [主题系统规范](#主题系统规范)
- [组件样式规范](#组件样式规范)
- [响应式设计](#响应式设计)
- [样式优化](#样式优化)
- [常见问题解决](#常见问题解决)

## 🏗️ 样式架构概述

### 1. 样式层级结构
```
样式优先级（从低到高）：
1. Omega 框架基础样式
2. TailwindCSS 工具类
3. ElementUI 组件样式
4. 自定义组件样式
5. 页面级样式
6. 内联样式（不推荐）
```

### 2. 样式文件组织原则
```
resources/
├── index.scss              # 样式入口，导入顺序很重要
├── var.scss                # SCSS变量定义
├── _theme.extend.scss      # 主题扩展变量
├── elementui/              # ElementUI定制样式
│   └── elementui-custom.scss
├── components/             # 通用组件样式
├── pages/                  # 页面特定样式
└── utils/                  # 样式工具类
    ├── mixins.scss         # SCSS混入
    └── functions.scss      # SCSS函数
```

### 3. 样式导入顺序规范
```scss
// resources/index.scss - 严格按此顺序导入
@import "~@omega/theme/index.scss";           // 1. 框架基础样式
@import "./utils/functions.scss";            // 2. SCSS函数
@import "./utils/mixins.scss";               // 3. SCSS混入
@import "./elementui/elementui-custom.scss"; // 4. 组件库定制
@import "./components/index.scss";           // 5. 通用组件样式
@import "./pages/index.scss";                // 6. 页面样式
```

## 🎨 CSS变量系统

### 1. 颜色变量规范

#### 1.1 主色系变量
```css
:root {
  /* 品牌主色 */
  --ZS: #1890ff;          /* 主色 - 用于重要按钮、链接 */
  --ZS-hover: #40a9ff;    /* 主色悬停态 */
  --ZS-active: #096dd9;   /* 主色激活态 */
  --ZS-disabled: #d9d9d9; /* 主色禁用态 */
  
  /* 辅助色系 */
  --F1: #52c41a;          /* 辅助色1 - 成功、确认 */
  --F2: #faad14;          /* 辅助色2 - 警告、提醒 */
  --F3: #f5222d;          /* 辅助色3 - 错误、危险 */
}
```

#### 1.2 文本颜色变量
```css
:root {
  /* 文本颜色层级 */
  --T1: rgba(0, 0, 0, 0.85);    /* 主要文本 - 标题、重要内容 */
  --T2: rgba(0, 0, 0, 0.65);    /* 次要文本 - 正文内容 */
  --T3: rgba(0, 0, 0, 0.45);    /* 辅助文本 - 说明、提示 */
  --T4: rgba(0, 0, 0, 0.25);    /* 禁用文本 */
}

/* 暗色主题下的文本颜色 */
[data-theme="dark"] {
  --T1: rgba(255, 255, 255, 0.85);
  --T2: rgba(255, 255, 255, 0.65);
  --T3: rgba(255, 255, 255, 0.45);
  --T4: rgba(255, 255, 255, 0.25);
}
```

#### 1.3 背景颜色变量
```css
:root {
  /* 背景色层级 */
  --BG: #ffffff;          /* 主背景 - 页面背景 */
  --BG1: #fafafa;         /* 次要背景 - 卡片背景 */
  --BG2: #f5f5f5;         /* 辅助背景 - 分割区域 */
  --BG3: #f0f0f0;         /* 边框背景 - 输入框等 */
}

/* 暗色主题背景 */
[data-theme="dark"] {
  --BG: #141414;
  --BG1: #1f1f1f;
  --BG2: #262626;
  --BG3: #434343;
}
```

### 2. 尺寸变量规范

#### 2.1 字体尺寸变量
```css
:root {
  /* 标题字号 */
  --H1: 24px;             /* 大标题 */
  --H2: 20px;             /* 中标题 */
  --H3: 16px;             /* 小标题 */
  --H4: 14px;             /* 次级标题 */
  
  /* 正文字号 */
  --B1: 16px;             /* 大正文 */
  --B2: 14px;             /* 标准正文 */
  --B3: 12px;             /* 小正文 */
  --B4: 10px;             /* 辅助文字 */
}
```

#### 2.2 间距变量
```css
:root {
  /* 间距系统 - 基于8px网格 */
  --J1: 8px;              /* 最小间距 */
  --J2: 16px;             /* 小间距 */
  --J3: 24px;             /* 中间距 */
  --J4: 32px;             /* 大间距 */
  --J5: 48px;             /* 超大间距 */
  
  /* 组件内边距 */
  --padding-xs: 4px 8px;
  --padding-sm: 8px 12px;
  --padding-md: 12px 16px;
  --padding-lg: 16px 24px;
  --padding-xl: 24px 32px;
}
```

#### 2.3 圆角变量
```css
:root {
  /* 圆角系统 */
  --radius-xs: 2px;       /* 最小圆角 */
  --radius-sm: 4px;       /* 小圆角 */
  --radius-md: 6px;       /* 中圆角 */
  --radius-lg: 8px;       /* 大圆角 */
  --radius-xl: 12px;      /* 超大圆角 */
  --radius-full: 50%;     /* 完全圆角 */
}
```

### 3. CSS变量使用规范

#### 3.1 正确使用方式
```css
/* ✅ 推荐：使用CSS变量 */
.card {
  background-color: var(--BG1);
  color: var(--T1);
  padding: var(--J2);
  border-radius: var(--radius-md);
  font-size: var(--B2);
}

/* ✅ 推荐：带回退值的CSS变量 */
.button {
  background-color: var(--ZS, #1890ff);
  color: var(--BG, #ffffff);
}
```

#### 3.2 错误使用方式
```css
/* ❌ 禁止：硬编码颜色 */
.card {
  background-color: #ffffff;
  color: #333333;
}

/* ❌ 禁止：硬编码尺寸 */
.title {
  font-size: 16px;
  margin-bottom: 12px;
}
```

## 🌈 主题系统规范

### 1. 主题配置结构
```scss
// resources/_theme.extend.scss
$omega_theme_extend: (
  "light": (
    // 登录页面主题
    LOGIN_BG: linear-gradient(90deg, rgba(231, 235, 246, 1) 0%, rgba(231, 251, 242, 1) 100%),
    LOGIN_LOGO_IMG: url("/static/image/login_logo_light.png"),
    
    // 自定义业务主题
    DASHBOARD_BG: #f0f2f5,
    CHART_PRIMARY: #1890ff,
    CHART_SUCCESS: #52c41a
  ),
  "dark": (
    LOGIN_BG: linear-gradient(rgb(3, 152, 216) 0%, rgb(99, 131, 255) 0%, rgb(34, 46, 102) 100%),
    LOGIN_LOGO_IMG: url("/static/image/login_logo_dark.png"),
    
    DASHBOARD_BG: #141414,
    CHART_PRIMARY: #177ddc,
    CHART_SUCCESS: #49aa19
  ),
  "blue": (
    // 蓝色主题配置
    LOGIN_BG: linear-gradient(135deg, #667eea 0%, #764ba2 100%),
    DASHBOARD_BG: #f0f5ff,
    CHART_PRIMARY: #2f54eb
  )
);
```

### 2. 主题切换实现
```javascript
// src/omega/theme.js
import { OmegaThemePlugin } from "@omega/theme/plugin";
import omegaApp from "@omega/app";

omegaApp.plugin.register(OmegaThemePlugin, {
  defaultActive: "light",           // 默认主题
  themeLimits: ["light", "dark"],   // 限制可用主题
  storageKey: "app-theme"           // 本地存储键名
});
```

### 3. 组件中的主题适配
```vue
<template>
  <div class="theme-adaptive-component">
    <div class="header" :class="themeClass">
      <h2>主题自适应组件</h2>
    </div>
    <div class="content">
      <p>这是内容区域</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "ThemeAdaptiveComponent",
  computed: {
    themeClass() {
      return `theme-${this.$omega.theme.current}`;
    }
  }
};
</script>

<style lang="scss" scoped>
.theme-adaptive-component {
  background-color: var(--BG1);
  color: var(--T1);
  
  .header {
    padding: var(--J2);
    border-bottom: 1px solid var(--BG3);
    
    &.theme-dark {
      border-color: var(--BG2);
    }
  }
  
  .content {
    padding: var(--J3);
    font-size: var(--B2);
  }
}
</style>
```

## 🧩 组件样式规范

### 1. 组件样式结构
```vue
<template>
  <div class="custom-component" :class="componentClass">
    <div class="custom-component__header">
      <slot name="header"></slot>
    </div>
    <div class="custom-component__body">
      <slot></slot>
    </div>
    <div class="custom-component__footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 使用BEM命名规范
.custom-component {
  // 组件根样式
  background-color: var(--BG1);
  border-radius: var(--radius-md);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  // 元素样式
  &__header {
    padding: var(--J2) var(--J3);
    border-bottom: 1px solid var(--BG3);
    font-weight: 600;
    color: var(--T1);
  }
  
  &__body {
    padding: var(--J3);
    color: var(--T2);
    line-height: 1.6;
  }
  
  &__footer {
    padding: var(--J2) var(--J3);
    border-top: 1px solid var(--BG3);
    text-align: right;
  }
  
  // 修饰符样式
  &--bordered {
    border: 1px solid var(--BG3);
  }
  
  &--shadow {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  // 状态样式
  &.is-loading {
    opacity: 0.6;
    pointer-events: none;
  }
  
  &.is-disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }
}
</style>
```

### 2. ElementUI组件定制
```scss
// resources/elementui/elementui-custom.scss

// 对话框定制
.el-dialog {
  border-radius: var(--radius-lg);
  
  .el-dialog__title {
    font-weight: 600;
    font-size: var(--H3);
    color: var(--T1);
  }
  
  .el-dialog__body {
    background-color: var(--BG);
    padding: var(--J1);
    
    .dialog-content {
      background-color: var(--BG1);
      border-radius: var(--radius-md);
      padding: var(--J2);
    }
  }
  
  .el-dialog__header {
    padding: var(--J2) var(--J3);
    border-bottom: 1px solid var(--BG3);
  }
}

// 表格定制
.el-table {
  background-color: var(--BG1);
  color: var(--T2);
  
  .el-table__header {
    background-color: var(--BG2);
    
    th {
      background-color: var(--BG2);
      color: var(--T1);
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: var(--BG2);
    }
  }
}

// 按钮定制
.el-button {
  border-radius: var(--radius-sm);
  font-weight: 500;
  
  &--primary {
    background-color: var(--ZS);
    border-color: var(--ZS);
    
    &:hover {
      background-color: var(--ZS-hover);
      border-color: var(--ZS-hover);
    }
    
    &:active {
      background-color: var(--ZS-active);
      border-color: var(--ZS-active);
    }
  }
}
```

### 3. 表单组件样式规范
```scss
// 表单容器
.form-container {
  background-color: var(--BG1);
  padding: var(--J3);
  border-radius: var(--radius-md);
  
  .form-title {
    font-size: var(--H3);
    font-weight: 600;
    color: var(--T1);
    margin-bottom: var(--J3);
    padding-bottom: var(--J2);
    border-bottom: 1px solid var(--BG3);
  }
  
  .form-section {
    margin-bottom: var(--J4);
    
    .section-title {
      font-size: var(--H4);
      font-weight: 500;
      color: var(--T1);
      margin-bottom: var(--J2);
    }
  }
  
  .form-actions {
    text-align: right;
    padding-top: var(--J2);
    border-top: 1px solid var(--BG3);
    
    .el-button + .el-button {
      margin-left: var(--J2);
    }
  }
}

// 表单项样式
.el-form-item {
  margin-bottom: var(--J3);
  
  .el-form-item__label {
    color: var(--T1);
    font-weight: 500;
    line-height: 1.4;
  }
  
  .el-form-item__content {
    .el-input,
    .el-select,
    .el-textarea {
      .el-input__inner,
      .el-textarea__inner {
        background-color: var(--BG);
        border-color: var(--BG3);
        color: var(--T2);
        
        &:focus {
          border-color: var(--ZS);
        }
        
        &::placeholder {
          color: var(--T3);
        }
      }
    }
  }
  
  // 必填项标识
  &.is-required {
    .el-form-item__label::before {
      content: '*';
      color: var(--F3);
      margin-right: 4px;
    }
  }
  
  // 错误状态
  &.is-error {
    .el-input__inner,
    .el-textarea__inner {
      border-color: var(--F3);
    }
  }
}
```

## 📱 响应式设计

### 1. 断点系统
```scss
// 断点变量定义
$breakpoints: (
  xs: 480px,    // 超小屏幕
  sm: 768px,    // 小屏幕
  md: 992px,    // 中等屏幕
  lg: 1200px,   // 大屏幕
  xl: 1600px    // 超大屏幕
);

// 媒体查询混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 使用示例
.responsive-component {
  padding: var(--J2);
  
  @include respond-to(sm) {
    padding: var(--J3);
  }
  
  @include respond-to(lg) {
    padding: var(--J4);
  }
}
```

### 2. 响应式布局
```vue
<template>
  <div class="responsive-layout">
    <div class="layout-header">
      <h1>响应式页面</h1>
    </div>
    <div class="layout-content">
      <div class="content-main">
        <div class="card-grid">
          <div class="card" v-for="item in items" :key="item.id">
            {{ item.title }}
          </div>
        </div>
      </div>
      <div class="content-sidebar">
        <div class="sidebar-content">侧边栏</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.responsive-layout {
  min-height: 100vh;
  background-color: var(--BG);
  
  .layout-header {
    padding: var(--J2) var(--J3);
    background-color: var(--BG1);
    border-bottom: 1px solid var(--BG3);
    
    h1 {
      font-size: var(--H2);
      color: var(--T1);
      margin: 0;
    }
  }
  
  .layout-content {
    display: flex;
    flex-direction: column;
    gap: var(--J3);
    padding: var(--J3);
    
    @include respond-to(md) {
      flex-direction: row;
    }
    
    .content-main {
      flex: 1;
      
      .card-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: var(--J2);
        
        @include respond-to(sm) {
          grid-template-columns: repeat(2, 1fr);
        }
        
        @include respond-to(lg) {
          grid-template-columns: repeat(3, 1fr);
        }
        
        .card {
          background-color: var(--BG1);
          padding: var(--J3);
          border-radius: var(--radius-md);
          border: 1px solid var(--BG3);
        }
      }
    }
    
    .content-sidebar {
      width: 100%;
      
      @include respond-to(md) {
        width: 300px;
        flex-shrink: 0;
      }
      
      .sidebar-content {
        background-color: var(--BG1);
        padding: var(--J3);
        border-radius: var(--radius-md);
        border: 1px solid var(--BG3);
      }
    }
  }
}
</style>
```

## ⚡ 样式优化

### 1. 性能优化技巧
```scss
// 使用 CSS 自定义属性进行动画优化
.animated-element {
  --scale: 1;
  --opacity: 1;
  
  transform: scale(var(--scale));
  opacity: var(--opacity);
  transition: transform 0.3s ease, opacity 0.3s ease;
  
  &:hover {
    --scale: 1.05;
    --opacity: 0.9;
  }
}

// 避免重排重绘的动画
.smooth-animation {
  will-change: transform, opacity;
  transform: translateZ(0); // 开启硬件加速
}

// 使用 contain 属性优化渲染
.isolated-component {
  contain: layout style paint;
}
```

### 2. 代码组织优化
```scss
// 使用 SCSS 混入减少重复代码
@mixin card-style($padding: var(--J2)) {
  background-color: var(--BG1);
  border-radius: var(--radius-md);
  border: 1px solid var(--BG3);
  padding: $padding;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@mixin button-variant($bg-color, $text-color: var(--BG)) {
  background-color: $bg-color;
  color: $text-color;
  border: 1px solid $bg-color;
  
  &:hover {
    background-color: lighten($bg-color, 10%);
    border-color: lighten($bg-color, 10%);
  }
  
  &:active {
    background-color: darken($bg-color, 5%);
    border-color: darken($bg-color, 5%);
  }
}

// 使用示例
.info-card {
  @include card-style(var(--J3));
}

.primary-button {
  @include button-variant(var(--ZS));
}

.success-button {
  @include button-variant(var(--F1));
}
```

## ❓ 常见问题解决

### 1. 主题切换问题
**问题**: 主题切换后某些组件样式不更新
```scss
// ❌ 错误：直接使用颜色值
.component {
  background-color: #ffffff;
}

// ✅ 正确：使用CSS变量
.component {
  background-color: var(--BG1);
}
```

### 2. 样式优先级问题
**问题**: 自定义样式被ElementUI样式覆盖
```scss
// ❌ 错误：使用!important
.el-button {
  background-color: red !important;
}

// ✅ 正确：提高选择器权重
.custom-form .el-button--primary {
  background-color: var(--ZS);
}

// ✅ 更好：使用深度选择器
.custom-form {
  ::v-deep .el-button--primary {
    background-color: var(--ZS);
  }
}
```

### 3. 响应式布局问题
**问题**: 移动端布局错乱
```scss
// ❌ 错误：固定宽度
.sidebar {
  width: 300px;
}

// ✅ 正确：响应式宽度
.sidebar {
  width: 100%;
  
  @include respond-to(md) {
    width: 300px;
  }
}
```

### 4. 样式隔离问题
**问题**: 组件样式相互影响
```vue
<!-- ❌ 错误：没有使用scoped -->
<style lang="scss">
.title {
  color: red;
}
</style>

<!-- ✅ 正确：使用scoped隔离 -->
<style lang="scss" scoped>
.title {
  color: var(--T1);
}
</style>
```

---

**文档版本**: v1.0.0  
**更新时间**: 2025-07-02  
**维护者**: 虚拟电厂平台开发团队
