<template>
  <div class="aggregation-page fullfilled relative flex flex-col">
    <!-- 搜索和操作区域 -->
    <div class="search-bar">
      <div class="search-left">
        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          placeholder="请输入关键字"
          prefix-icon="el-icon-search"
          size="small"
          style="width: 240px;"
        />

        <!-- 机组类型下拉选择 -->
        <el-select
          v-model="selectedType"
          placeholder="机组类型"
          size="small"
          style="width: 240px;"
        >
          <el-option label="全部" value="" />
          <el-option label="储能机组" value="储能机组" />
          <el-option label="光伏机组" value="光伏机组" />
          <el-option label="风电机组" value="风电机组" />
          <el-option label="负荷机组" value="负荷机组" />
        </el-select>
      </div>

      <!-- 右侧新增按钮 -->
      <div class="search-right">
        <el-button type="primary" size="small" @click="handleAdd">
          新增
        </el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="currentPageData"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column
        prop="index"
        label="序号"
        width="80"
        align="center"
      >
        <template slot-scope="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>

      <el-table-column
        prop="name"
        label="机组名称"
        min-width="200"
        show-overflow-tooltip
      />

      <el-table-column
        prop="type"
        label="机组类型"
        min-width="150"
        show-overflow-tooltip
      />

      <el-table-column
        prop="resourceCount"
        label="聚合资源数量"
        min-width="150"
        align="center"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.resourceCount }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="200"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            style="color: #f56c6c"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-wrapper">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="filteredData.length"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'AggregationPage',
  data() {
    return {
      // 搜索条件
      searchKeyword: '',
      selectedType: '',

      // 分页参数
      currentPage: 1,
      pageSize: 10,

      // 表格高度
      tableHeight: 400,

      // 模拟表格数据
      tableData: [
        {
          name: '储能机组001',
          type: '储能机组',
          resourceCount: 15
        },
        {
          name: '光伏机组002',
          type: '光伏机组',
          resourceCount: 28
        },
        {
          name: '风电机组003',
          type: '风电机组',
          resourceCount: 12
        },
        {
          name: '负荷机组004',
          type: '负荷机组',
          resourceCount: 35
        },
        {
          name: '储能机组005',
          type: '储能机组',
          resourceCount: 22
        },
        {
          name: '光伏机组006',
          type: '光伏机组',
          resourceCount: 18
        },
        {
          name: '风电机组007',
          type: '风电机组',
          resourceCount: 9
        },
        {
          name: '负荷机组008',
          type: '负荷机组',
          resourceCount: 41
        }
      ]
    }
  },
  computed: {
    // 过滤后的数据
    filteredData() {
      let data = this.tableData

      // 关键字搜索
      if (this.searchKeyword) {
        data = data.filter(item =>
          item.name.includes(this.searchKeyword) ||
          item.type.includes(this.searchKeyword)
        )
      }

      // 类型筛选
      if (this.selectedType) {
        data = data.filter(item => item.type === this.selectedType)
      }

      return data
    },

    // 当前页数据
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredData.slice(start, end)
    }
  },
  mounted() {
    this.calculateTableHeight()
    window.addEventListener('resize', this.calculateTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight)
  },
  methods: {
    // 计算表格高度
    calculateTableHeight() {
      // 计算可用高度，避免出现纵向滚动条
      const windowHeight = window.innerHeight
      const searchBarHeight = 80 // 搜索栏高度
      const paginationHeight = 80 // 分页高度
      const padding = 80 // 内边距和其他元素高度

      this.tableHeight = windowHeight - searchBarHeight - paginationHeight - padding

      // 确保最小高度
      if (this.tableHeight < 300) {
        this.tableHeight = 300
      }
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
    },

    // 新增
    handleAdd() {
      this.$message.info('新增功能待实现')
    },

    // 详情
    handleDetail(row) {
      this.$message.info(`查看机组详情：${row.name}`)
    },

    // 编辑
    handleEdit(row) {
      this.$message.info(`编辑机组：${row.name}`)
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除机组"${row.name}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.aggregation-page {
  // padding: 20px;
  height: 100%;
  // display: flex;
  // flex-direction: column;
  // overflow: hidden;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  // align-items: center;
  margin-bottom: 16px;
  // background: white;
  // border-radius: 4px;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-right {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  background: white;
  border-top: 1px solid #ebeef5;
  margin-top: auto;
}

// Element UI 表格样式定制
::v-deep .el-table {
  flex: 1;

  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #fafcff;
        color: #424e5f;
        font-weight: normal;
        font-size: 12px;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      td {
        font-size: 14px;
        color: #13171f;
      }
    }
  }
}

// Element UI 按钮样式定制
::v-deep .el-button--text {
  padding: 0;
  margin: 0 8px;

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }
}

// Element UI 输入框样式定制
::v-deep .el-input__inner {
  font-size: 14px;
}

// Element UI 选择器样式定制
::v-deep .el-select .el-input__inner {
  font-size: 14px;
}

// Element UI 分页样式定制
::v-deep .el-pagination {
  .el-pagination__total,
  .el-pagination__sizes,
  .el-pagination__jump {
    color: #606266;
    font-size: 13px;
  }
}
</style>