# 虚拟电厂平台 Cursor AI 开发规则

## 项目概述
这是一个基于Vue 2.7.8 + ElementUI + TailwindCSS + Omega框架的微前端Monorepo项目，采用pnpm工作区管理。

## 核心技术栈
- Vue 2.7.8 + Vue Router 3.5.3 + Vuex 3.5.1
- ElementUI 2.15.6 + TailwindCSS 3.1.6
- Omega框架系列 (@omega/app, @omega/http, @omega/theme等)
- SCSS + PostCSS
- Vue CLI 5.0.8 + Webpack
- pnpm Monorepo

## 代码生成规则

### 1. Vue组件生成规范
生成Vue组件时必须遵循以下模板：

```vue
<template>
  <div class="component-name" :class="componentClass">
    <div class="component-name__header" v-if="title">
      <h3 class="text-H3 font-semibold text-T1">{{ title }}</h3>
    </div>
    <div class="component-name__body">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "ComponentName",
  props: {
    title: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      default: "default",
      validator: value => ["default", "primary", "success"].includes(value)
    }
  },
  computed: {
    componentClass() {
      return {
        [`component-name--${this.type}`]: this.type !== "default"
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.component-name {
  background-color: var(--BG1);
  border-radius: var(--radius-md);
  padding: var(--J2);
  
  &__header {
    margin-bottom: var(--J2);
    padding-bottom: var(--J1);
    border-bottom: 1px solid var(--BG3);
  }
  
  &__body {
    color: var(--T2);
  }
  
  &--primary {
    border: 1px solid var(--ZS);
  }
  
  &--success {
    border: 1px solid var(--F1);
  }
}
</style>
```

### 2. API接口文件生成规范
在 `src/api/` 目录下生成接口文件，文件名使用小驼峰命名：

```typescript
// 接口描述注释
// path: /api/v1/module/action
// desc: 接口功能描述

export const API_CONSTANT_NAME = '/api/v1/module/action'

// 请求参数接口
export interface IApiActionReq {
  id: string; // 参数描述
  name?: string; // 可选参数描述
}

// 响应数据接口
export interface IApiActionResp {
  id: string; // 返回字段描述
  status: string; // 状态描述
}

// 错误码枚举（如需要）
export enum ApiActionErrorCode {
  INVALID_PARAMS = 1001,
  NOT_FOUND = 1002,
  SYSTEM_ERROR = 1003
}
```

### 3. 路由配置生成规范
```javascript
// router/index.js
export const appRoutes = [
  {
    path: "/",
    component: () => import("./main.vue"),
    children: [
      {
        path: "",
        redirect: "/default-page"
      },
      {
        path: "/module-page",
        component: () => import("@/projects/module/index.vue"),
        meta: {
          keepAlive: true,
          title: "页面标题"
        }
      }
    ]
  }
];

export default {
  routes: appRoutes
};
```

### 4. 样式生成规范
必须使用CSS变量，禁止硬编码：

```scss
// ✅ 正确的样式写法
.page-container {
  background-color: var(--BG);
  color: var(--T1);
  padding: var(--J3);
  border-radius: var(--radius-lg);
  
  .page-header {
    font-size: var(--H2);
    margin-bottom: var(--J3);
    color: var(--T1);
  }
  
  .page-content {
    font-size: var(--B2);
    line-height: 1.6;
    color: var(--T2);
  }
}

// 响应式设计
@include respond-to(md) {
  .page-container {
    padding: var(--J4);
  }
}
```

### 5. 表单组件生成规范
```vue
<template>
  <div class="form-container">
    <div class="form-title">{{ title }}</div>
    <el-form
      ref="form"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="custom-form"
    >
      <el-form-item label="字段名称" prop="fieldName">
        <el-input
          v-model="formData.fieldName"
          placeholder="请输入字段名称"
        />
      </el-form-item>
      
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "CustomForm",
  props: {
    title: {
      type: String,
      default: "表单标题"
    }
  },
  data() {
    return {
      formData: {
        fieldName: ""
      },
      formRules: {
        fieldName: [
          { required: true, message: "请输入字段名称", trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 提交逻辑
          this.$emit("submit", this.formData);
        }
      });
    },
    handleCancel() {
      this.$emit("cancel");
    }
  }
};
</script>

<style lang="scss" scoped>
.form-container {
  background-color: var(--BG1);
  padding: var(--J3);
  border-radius: var(--radius-md);
  
  .form-title {
    font-size: var(--H3);
    font-weight: 600;
    color: var(--T1);
    margin-bottom: var(--J3);
    padding-bottom: var(--J2);
    border-bottom: 1px solid var(--BG3);
  }
  
  .form-actions {
    text-align: right;
    padding-top: var(--J2);
    border-top: 1px solid var(--BG3);
    
    .el-button + .el-button {
      margin-left: var(--J2);
    }
  }
}
</style>
```

## 强制要求

### 1. 样式规范
- 必须使用CSS变量，禁止硬编码颜色和尺寸
- 必须使用scoped样式
- 必须支持明暗主题切换
- 必须使用BEM命名规范
- 禁止使用!important（除非绝对必要）

### 2. 组件规范
- 组件名必须使用PascalCase
- Props必须定义类型和默认值
- 必须添加适当的注释
- 事件名使用kebab-case

### 3. 代码质量
- 必须通过ESLint检查
- 复杂逻辑必须添加注释
- 避免在模板中使用复杂表达式
- 合理使用计算属性和方法

### 4. 性能优化
- 使用路由懒加载
- 合理使用v-show和v-if
- 大型列表使用虚拟滚动
- 避免不必要的响应式数据

## CSS变量参考

### 颜色变量
```css
/* 主色系 */
--ZS: 主色
--F1: 辅助色1 (成功)
--F2: 辅助色2 (警告)  
--F3: 辅助色3 (危险)

/* 文本色 */
--T1: 主文本色
--T2: 次要文本色
--T3: 辅助文本色

/* 背景色 */
--BG: 主背景色
--BG1: 次要背景色
--BG2: 辅助背景色
--BG3: 边框背景色
```

### 尺寸变量
```css
/* 字号 */
--H1: 24px  /* 大标题 */
--H2: 20px  /* 中标题 */
--H3: 16px  /* 小标题 */
--B1: 16px  /* 大正文 */
--B2: 14px  /* 标准正文 */
--B3: 12px  /* 小正文 */

/* 间距 */
--J1: 8px   /* 最小间距 */
--J2: 16px  /* 小间距 */
--J3: 24px  /* 中间距 */
--J4: 32px  /* 大间距 */

/* 圆角 */
--radius-sm: 4px
--radius-md: 6px
--radius-lg: 8px
```

## 响应式断点
```scss
$breakpoints: (
  xs: 480px,
  sm: 768px,
  md: 992px,
  lg: 1200px,
  xl: 1600px
);
```

## 插件开发特殊要求

### 1. 插件配置文件
在 `public/config.js` 中配置插件信息：

```javascript
__registeAppConfig.setAppconfig({
  version: "1.0.0",
  navmenu: [
    {
      label: "菜单名称",
      icon: "icon-name",
      type: "subMenu",
      subMenuList: [
        {
          label: "子菜单",
          type: "menuItem",
          location: "/path",
          permission: "permission-code"
        }
      ]
    }
  ]
});
```

### 2. Omega插件注册
```javascript
// src/omega/index.js
import omegaApp from "@omega/app";
import { OmegaHttpPlugin } from "@omega/http";
import { OmegaI18nPlugin } from "@omega/i18n/plugin";
import { OmegaThemePlugin } from "@omega/theme/plugin";

omegaApp.plugin.register(OmegaHttpPlugin, {});
omegaApp.plugin.register(OmegaI18nPlugin, {
  defaultActive: "zh"
});
omegaApp.plugin.register(OmegaThemePlugin, {
  defaultActive: "light"
});
```

## 错误处理规范

### 1. API错误处理
```javascript
try {
  const result = await api.request();
  // 处理成功结果
} catch (error) {
  console.error("API请求失败:", error);
  this.$message.error(error.message || "操作失败");
}
```

### 2. 组件错误边界
```javascript
errorCaptured(err, instance, info) {
  console.error("组件错误:", err, info);
  return false;
}
```

## 代码生成偏好

1. 优先使用组合式API（Vue 2.7支持）
2. 使用TypeScript类型定义（如果项目支持）
3. 遵循现有的代码风格和架构模式
4. 生成完整的错误处理逻辑
5. 包含适当的注释和文档
6. 考虑性能优化和用户体验
7. 确保代码的可维护性和可扩展性

---

**重要提醒**: 所有生成的代码都必须严格遵循以上规范，特别是样式规范和组件规范。在不确定的情况下，优先参考项目中现有的代码实现。
