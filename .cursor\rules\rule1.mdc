---
description: 
globs: *.vue,*.css
alwaysApply: false
---

## 🔧 插件开发速查

### 创建新插件步骤
1. 在 `plugins/` 下创建插件目录
2. 复制现有插件结构
3. 修改 `package.json` 配置
4. 配置 `public/config.js` 菜单
5. 开发业务功能
6. 测试集成

### 插件目录结构
```
plugins/your-plugin/
├── public/
│   ├── config.js          # 插件配置
│   └── index.html
├── src/
│   ├── main.js            # 入口文件
│   ├── omega/             # 框架配置
│   ├── router/            # 路由配置
│   ├── projects/          # 业务模块
│   └── resources/         # 样式资源
├── package.json
└── vue.config.js
```

### 关键配置文件

#### package.json
```json
{
  "name": "your-plugin-name",
  "version": "1.0.0",
  "scripts": {
    "dev": "vue-cli-service serve",
    "build": "vue-cli-service build"
  }
}
```

#### public/config.js
```javascript
__registeAppConfig.setAppconfig({
  version: "1.0.0",
  navmenu: [
    {
      label: "菜单名称",
      icon: "icon-name",
      type: "subMenu",
      subMenuList: [
        {
          label: "子菜单",
          type: "menuItem",
          location: "/path",
          permission: "permission-code"
        }
      ]
    }
  ]
});
```

## 🎨 样式开发速查

### CSS变量快速参考

#### 颜色变量
```css
/* 主色系 */
--ZS: 主色
--F1: 辅助色1 (成功)
--F2: 辅助色2 (警告)
--F3: 辅助色3 (危险)

/* 文本色 */
--T1: 主文本色
--T2: 次要文本色
--T3: 辅助文本色

/* 背景色 */
--BG: 主背景色
--BG1: 次要背景色
--BG2: 辅助背景色
--BG3: 边框背景色
```

#### 尺寸变量
```css
/* 字号 */
--H1: 24px  /* 大标题 */
--H2: 20px  /* 中标题 */
--H3: 16px  /* 小标题 */
--B1: 16px  /* 大正文 */
--B2: 14px  /* 标准正文 */
--B3: 12px  /* 小正文 */

/* 间距 */
--J1: 8px   /* 最小间距 */
--J2: 16px  /* 小间距 */
--J3: 24px  /* 中间距 */
--J4: 32px  /* 大间距 */
--J5: 48px  /* 超大间距 */

/* 圆角 */
--radius-sm: 4px
--radius-md: 6px
--radius-lg: 8px
```

### 样式使用规范

#### ✅ 推荐做法
```css
.component {
  background-color: var(--BG1);
  color: var(--T1);
  padding: var(--J2);
  border-radius: var(--radius-md);
  font-size: var(--B2);
}
```

#### ❌ 禁止做法
```css
.component {
  background-color: #ffffff;  /* 硬编码颜色 */
  color: #333333;
  padding: 16px;              /* 硬编码尺寸 */
  border-radius: 6px;
  font-size: 14px;
}
```

### 路由配置模板
```javascript
// router/index.js
export const appRoutes = [
  {
    path: "/",
    component: () => import("./main.vue"),
    children: [
      {
        path: "",
        redirect: "/dashboard"
      },
      {
        path: "/dashboard",
        component: () => import("@/projects/dashboard/index.vue"),
        meta: {
          keepAlive: true,
          title: "仪表盘"
        }
      }
    ]
  }
];
```

## 🔌 Omega插件系统

### 插件注册
```javascript
// src/omega/index.js
import omegaApp from "@omega/app";
import { OmegaHttpPlugin } from "@omega/http";
import { OmegaI18nPlugin } from "@omega/i18n/plugin";
import { OmegaThemePlugin } from "@omega/theme/plugin";

// HTTP插件
omegaApp.plugin.register(OmegaHttpPlugin, {});

// 国际化插件
omegaApp.plugin.register(OmegaI18nPlugin, {
  defaultActive: "zh",
  en: require("../config/lang/en.json")
});

// 主题插件
omegaApp.plugin.register(OmegaThemePlugin, {
  defaultActive: "light",
  themeLimits: ["light", "dark"]
});
```

### API调用
```javascript
import { api } from "@altair/knight";

// 路由跳转
api.subRouterQuery({
  path: "/target-page",
  query: { id: 123 }
});

// 获取路由参数
const { id } = api.getRouterQuery();
```

## 📱 响应式开发

### 断点系统
```scss
$breakpoints: (
  xs: 480px,
  sm: 768px,
  md: 992px,
  lg: 1200px,
  xl: 1600px
);

@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}
```

### 响应式布局
```scss
.responsive-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--J2);
  
  @include respond-to(sm) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include respond-to(lg) {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 🛠️ 常用工具

### SCSS混入
```scss
// 卡片样式混入
@mixin card-style($padding: var(--J2)) {
  background-color: var(--BG1);
  border-radius: var(--radius-md);
  border: 1px solid var(--BG3);
  padding: $padding;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 按钮变体混入
@mixin button-variant($bg-color) {
  background-color: $bg-color;
  border-color: $bg-color;
  
  &:hover {
    background-color: lighten($bg-color, 10%);
  }
}
```

### 常用组件样式
```scss
// 表单容器
.form-container {
  @include card-style(var(--J3));
  
  .form-title {
    font-size: var(--H3);
    color: var(--T1);
    margin-bottom: var(--J3);
    border-bottom: 1px solid var(--BG3);
    padding-bottom: var(--J2);
  }
}

// 数据表格
.data-table {
  background-color: var(--BG1);
  border-radius: var(--radius-md);
  overflow: hidden;
  
  .table-header {
    background-color: var(--BG2);
    padding: var(--J2);
    font-weight: 600;
  }
}
```

## 📋 检查清单

### 开发完成检查
- [ ] 使用CSS变量而非硬编码
- [ ] 支持明暗主题切换
- [ ] 响应式布局适配
- [ ] 组件样式使用scoped
- [ ] 路由配置正确
- [ ] 菜单配置完整
- [ ] 国际化配置
---


