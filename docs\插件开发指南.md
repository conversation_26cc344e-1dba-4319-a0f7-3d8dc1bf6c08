# 虚拟电厂平台插件开发指南

## 📋 目录
- [插件运行流程](#插件运行流程)
- [插件开发规范](#插件开发规范)
- [样式开发规范](#样式开发规范)
- [配置文件说明](#配置文件说明)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 🔄 插件运行流程

### 1. 插件加载机制

#### 1.1 项目结构
```
virtual-power-plant-web/
├── plugins/                    # 插件目录
│   └── vpp-resource-manager/   # 具体插件
│       ├── public/
│       │   └── config.js       # 插件配置文件
│       ├── src/
│       │   ├── main.js         # 插件入口文件
│       │   ├── omega/          # Omega框架配置
│       │   ├── router/         # 路由配置
│       │   ├── projects/       # 业务模块
│       │   └── resources/      # 样式资源
│       └── package.json
├── Fusion-template/            # 主应用模板
│   ├── main/                   # 主应用
│   └── sub/                    # 子应用
└── package.json               # 根配置
```

#### 1.2 插件启动流程
1. **环境准备**
   ```bash
   # 安装依赖
   pnpm install
   
   # 启动插件
   pnpm dev:vpp
   ```

2. **插件初始化**
   - 加载 `src/main.js` 入口文件
   - 注册 Omega 插件系统
   - 初始化路由、状态管理、主题等

3. **配置加载**
   - 读取 `public/config.js` 配置文件
   - 注册菜单、权限、国际化等配置

4. **应用启动**
   - 创建 Vue 应用实例
   - 挂载到 DOM 节点

### 2. 插件与主应用集成

#### 2.1 微前端集成
```javascript
// Fusion-template/main/src/omega/fusion.js
const develop = {
  appList: [
    {
      name: "vpp-resource-manager",
      url: "//localhost:9528"
    }
  ],
  options: {
    systemConfig: "local",
    isMultiProject: true
  }
};
```

#### 2.2 路由集成
插件路由会自动集成到主应用的路由系统中，支持：
- 独立路由管理
- 路由懒加载
- 路由守卫
- 缓存控制

## 🛠️ 插件开发规范

### 1. 目录结构规范

```
src/
├── api/                    # API 接口
│   └── custom.js
├── config/                 # 配置文件
│   ├── enums.js           # 枚举定义
│   └── lang/              # 国际化
├── hooks/                  # 自定义 Hooks
├── icons/                  # SVG 图标
├── omega/                  # Omega 框架配置
│   ├── index.js           # 插件注册入口
│   ├── http.js            # HTTP 配置
│   ├── i18n.js            # 国际化配置
│   ├── theme.js           # 主题配置
│   └── subApp.js          # 子应用配置
├── projects/               # 业务模块
│   └── [module-name]/     # 具体业务模块
├── resources/              # 样式资源
│   ├── index.css
│   ├── index.scss
│   ├── var.scss           # SCSS 变量
│   └── elementui/         # ElementUI 定制样式
├── router/                 # 路由配置
│   ├── index.js
│   └── main.vue
├── store/                  # 状态管理
└── main.js                # 入口文件
```

### 2. 插件入口文件规范

```javascript
// src/main.js
import omegaApp from "@omega/app";
import "./omega/index";
import "./resources/index.css";
import "./resources/index.scss";
import "./icons/index.js";

import Vue from "vue";
import ElementUI from "element-ui";
import routerOption from "./router";
import storeOption from "./store";

// 插件配置
Vue.use(ElementUI, { size: "small" });

// 启动应用
omegaApp.createApp({
  el: "#app",
  config: {},
  routerOption,
  storeOption
});
```

### 3. Omega 插件注册规范

```javascript
// src/omega/index.js
import "./i18n.js";
import "./theme.js";
import "./http.js";
import "./subApp.js";

import Vue from "vue";
import OmegaIcon from "@omega/icon";

Vue.use(OmegaIcon);
```

### 4. 路由配置规范

```javascript
// src/router/index.js
import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        path: "",
        redirect: "/default-page"
      },
      {
        meta: {
          keepAlive: true  // 页面缓存
        },
        path: "/module-page",
        component: () => import("@/projects/module/index.vue")
      }
    ]
  }
];

export default {
  routes: appRoutes
};
```

## 🎨 样式开发规范

### 1. 样式架构

#### 1.1 样式文件组织
```
resources/
├── index.css              # 全局样式入口
├── index.scss             # SCSS 样式入口
├── var.scss               # SCSS 变量文件
├── _theme.extend.scss     # 主题扩展
└── elementui/
    └── elementui-custom.scss  # ElementUI 定制
```

#### 1.2 样式导入顺序
```scss
// resources/index.scss
@import "~@omega/theme/index.scss";     // Omega 主题
@import "./elementui/elementui-custom.scss";  // ElementUI 定制
```

### 2. 主题系统规范

#### 2.1 主题变量定义
```scss
// resources/_theme.extend.scss
$omega_theme_extend: (
  "light": (
    LOGIN_BG: linear-gradient(90deg, rgba(231, 235, 246, 1) 0%, rgba(231, 251, 242, 1) 100%),
    LOGIN_LOGO_IMG: url("/static/image/login_logo_light.png")
  ),
  "dark": (
    LOGIN_BG: linear-gradient(rgb(3, 152, 216) 0%, rgb(99, 131, 255) 0%, rgb(34, 46, 102) 100%),
    LOGIN_LOGO_IMG: url("/static/image/login_logo_dark.png")
  )
);
```

#### 2.2 CSS 变量使用
```css
.page-container {
  background-color: var(--BG1);    /* 背景色 */
  color: var(--T1);                /* 主文本色 */
  font-size: var(--H3);            /* 标题字号 */
  padding: var(--J2);              /* 间距 */
}
```

### 3. TailwindCSS 集成规范

#### 3.1 配置文件
```javascript
// tailwind.config.js
module.exports = {
  darkMode: "class",
  content: ["./src/projects/**/*.{vue,html,js,md}"],
  theme: {
    extend: {
      colors: {
        ZS: "var(--ZS)",      // 主色
        F1: "var(--F1)",      // 辅助色1
        F2: "var(--F2)",      // 辅助色2
        Sta1: "var(--Sta1)",  // 成功色
        Sta2: "var(--Sta2)",  // 警告色
        Sta3: "var(--Sta3)",  // 危险色
      }
    }
  }
};
```

#### 3.2 使用规范
```vue
<template>
  <div class="bg-ZS text-T1 p-J2 rounded-lg">
    <h3 class="text-H3 font-semibold mb-J1">标题</h3>
    <p class="text-T2">内容文本</p>
  </div>
</template>
```

### 4. ElementUI 定制规范

#### 4.1 组件样式定制
```scss
// resources/elementui/elementui-custom.scss
.el-dialog {
  border-radius: 12px;
  
  .el-dialog__title {
    font-weight: 600;
    line-height: 22px;
    font-size: var(--H3);
  }
  
  .el-dialog__body {
    background-color: var(--BG);
    padding: 8px;
    
    .dialog-content {
      border-radius: 8px;
      background-color: var(--BG1);
      padding: var(--J2);
    }
  }
}
```

#### 4.2 响应式适配
```scss
// 子应用状态下的样式适配
body {
  position: relative !important;
}

.el-popper,
.el-tooltip__popper {
  position: absolute !important;
}
```

### 5. 样式开发注意事项

#### 5.1 必须遵循的规范
1. **使用 CSS 变量**：所有颜色、字号、间距必须使用预定义的 CSS 变量
2. **支持主题切换**：样式必须兼容明暗主题
3. **响应式设计**：考虑不同屏幕尺寸的适配
4. **组件隔离**：使用 scoped 样式避免全局污染

#### 5.2 禁止的做法
1. ❌ 硬编码颜色值：`color: #333333`
2. ❌ 硬编码尺寸：`font-size: 14px`
3. ❌ 全局样式污染：不使用 scoped
4. ❌ 内联样式：`style="color: red"`

#### 5.3 推荐的做法
1. ✅ 使用 CSS 变量：`color: var(--T1)`
2. ✅ 使用预定义尺寸：`font-size: var(--H3)`
3. ✅ 使用 scoped 样式：`<style lang="scss" scoped>`
4. ✅ 使用 TailwindCSS 类：`class="bg-ZS text-T1"`

## ⚙️ 配置文件说明

### 1. 插件配置文件

#### 1.1 public/config.js
```javascript
__registeAppConfig.setAppconfig({
  version: "1.0.0",
  
  // 国际化配置
  i18n: {
    en: {
      示例: "example",
      表格: "grid"
    }
  },
  
  // 菜单配置
  navmenu: [
    {
      label: "资源管理",
      icon: "permission-management-lin",
      type: "subMenu",
      subMenuList: [
        {
          label: "资源聚合",
          category: "platform",
          type: "menuItem",
          location: "/agg",
          permission: "project-in"
        }
      ]
    }
  ],
  
  // 新手引导配置
  newGuideSteps: [
    {
      name: "新手引导",
      children: [
        {
          step: 1,
          path: "/noviceGuidePage1",
          name: "引导页面1"
        }
      ]
    }
  ]
});
```

### 2. 构建配置

#### 2.1 vue.config.js
```javascript
const omegaCliDevserverHandler = require("@omega/cli-devserver");
const package = require("./package.json");

module.exports = omegaCliDevserverHandler({
  productionSourceMap: false,
  transpileDependencies: ["@omega"],
  outputDir: package.name + "-" + package.version,
  publicPath: "./",
  
  css: {
    loaderOptions: {
      scss: {
        additionalData: `@import "@/resources/var.scss";`
      },
      postcss: {
        postcssOptions: {
          plugins: [
            require("postcss-import"),
            require("tailwindcss/nesting"),
            require("tailwindcss")
          ]
        }
      }
    }
  }
});
```

## 🚀 最佳实践

### 1. 开发流程
1. **创建插件目录**：在 `plugins/` 下创建新插件
2. **复制模板结构**：参考现有插件的目录结构
3. **配置插件信息**：修改 `package.json` 和 `config.js`
4. **开发业务功能**：在 `projects/` 下创建业务模块
5. **测试集成**：在主应用中测试插件功能

### 2. 代码规范
1. **使用 TypeScript**：提高代码质量
2. **遵循 ESLint 规则**：保持代码风格一致
3. **编写单元测试**：确保代码质量
4. **文档完善**：为每个模块编写说明文档

### 3. 性能优化
1. **路由懒加载**：使用动态导入
2. **组件按需加载**：避免全量导入
3. **图片优化**：使用适当的图片格式和尺寸
4. **缓存策略**：合理使用页面缓存

## ❓ 常见问题

### 1. 样式问题
**Q: 样式在主应用中显示异常？**
A: 检查是否正确使用了 CSS 变量，确保样式文件正确导入。

**Q: 主题切换不生效？**
A: 确认主题配置文件是否正确，检查 CSS 变量定义。

### 2. 路由问题
**Q: 路由跳转失败？**
A: 检查路由配置是否正确，确认路径拼写无误。

**Q: 页面缓存不生效？**
A: 检查路由配置中的 `meta.keepAlive` 设置。

### 3. 构建问题
**Q: 构建失败？**
A: 检查依赖版本是否兼容，确认构建配置正确。

**Q: 热更新不工作？**
A: 检查开发服务器配置，确认文件监听正常。

### 4. 集成问题
**Q: 插件无法在主应用中加载？**
A: 检查主应用的 fusion.js 配置，确认插件名称和端口正确。

**Q: API 请求失败？**
A: 检查代理配置，确认后端服务地址正确。

## 📚 附录

### A. 常用 CSS 变量参考

#### A.1 颜色变量
```css
/* 主色系 */
--ZS: 主色
--F1: 辅助色1
--F2: 辅助色2

/* 文本色 */
--T1: 主文本色
--T2: 次要文本色
--T3: 辅助文本色

/* 背景色 */
--BG: 主背景色
--BG1: 次要背景色
--BG2: 辅助背景色

/* 状态色 */
--Sta1: 成功色
--Sta2: 警告色
--Sta3: 危险色
```

#### A.2 尺寸变量
```css
/* 字号 */
--H1: 大标题
--H2: 中标题
--H3: 小标题
--B1: 正文大
--B2: 正文中
--B3: 正文小

/* 间距 */
--J1: 小间距
--J2: 中间距
--J3: 大间距
```

### B. 插件开发模板

#### B.1 创建新插件脚本
```bash
#!/bin/bash
# create-plugin.sh

PLUGIN_NAME=$1
if [ -z "$PLUGIN_NAME" ]; then
  echo "请提供插件名称"
  exit 1
fi

# 创建插件目录
mkdir -p plugins/$PLUGIN_NAME

# 复制模板文件
cp -r plugins/vpp-resource-manager/* plugins/$PLUGIN_NAME/

# 更新 package.json
sed -i "s/vpp-resource-manager/$PLUGIN_NAME/g" plugins/$PLUGIN_NAME/package.json

echo "插件 $PLUGIN_NAME 创建完成"
```

#### B.2 插件开发检查清单
- [ ] 目录结构符合规范
- [ ] package.json 配置正确
- [ ] config.js 菜单配置完整
- [ ] 路由配置正确
- [ ] 样式使用 CSS 变量
- [ ] 支持主题切换
- [ ] 国际化配置完整
- [ ] 代码通过 ESLint 检查
- [ ] 功能测试通过
- [ ] 文档编写完整

### C. 调试技巧

#### C.1 开发环境调试
```javascript
// 在组件中添加调试信息
export default {
  name: "DebugComponent",
  mounted() {
    console.log("组件挂载", this.$route);
    console.log("主题变量", getComputedStyle(document.documentElement));
  }
};
```

#### C.2 样式调试
```css
/* 临时调试样式 */
.debug-border {
  border: 1px solid red !important;
}

.debug-bg {
  background-color: rgba(255, 0, 0, 0.1) !important;
}
```

#### C.3 路由调试
```javascript
// router/index.js
const router = new VueRouter({
  routes: appRoutes
});

// 添加路由守卫进行调试
router.beforeEach((to, from, next) => {
  console.log("路由跳转", { from: from.path, to: to.path });
  next();
});
```

### D. 性能优化建议

#### D.1 代码分割
```javascript
// 使用动态导入进行代码分割
const LazyComponent = () => import(
  /* webpackChunkName: "lazy-component" */
  "@/projects/module/LazyComponent.vue"
);
```

#### D.2 图片优化
```javascript
// vue.config.js 中配置图片优化
module.exports = {
  chainWebpack: config => {
    config.module
      .rule('images')
      .use('url-loader')
      .tap(options => {
        options.limit = 10240; // 10KB 以下转 base64
        return options;
      });
  }
};
```

#### D.3 缓存策略
```javascript
// 路由级别的缓存控制
{
  path: '/heavy-page',
  component: HeavyPage,
  meta: {
    keepAlive: true,
    maxAge: 300000 // 5分钟缓存
  }
}
```

---

## 📞 技术支持

### 联系方式
- **技术文档**: 查阅项目 Wiki
- **问题反馈**: 提交 Issue 到项目仓库
- **技术交流**: 加入开发者群组

### 相关资源
- [Omega 框架文档](https://omega-docs.example.com)
- [Vue.js 官方文档](https://vuejs.org)
- [ElementUI 文档](https://element.eleme.io)
- [TailwindCSS 文档](https://tailwindcss.com)

**文档版本**: v1.0.0
**更新时间**: 2025-07-02
**维护者**: 虚拟电厂平台开发团队
