<template>
  <div id="nav">
    <router-link
      v-for="(item, index) in appRoutes"
      :to="item.path"
      :key="index"
    >
      {{ item.path }}
    </router-link>
    <!-- <router-link to="/about">关于</router-link> -->
  </div>
</template>

<script>
import { appRoutes } from "@/router";
export default {
  name: "Menu",
  data() {
    return {
      appRoutes
    };
  }
};
</script>

<style>
#nav {
  padding: 10% 20%;
  display: flex;
  gap: 1rem;
}
</style>
