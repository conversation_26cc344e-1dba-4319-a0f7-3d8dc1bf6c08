# 虚拟电厂平台项目开发规范

## 项目结构

### 1. 项目架构概览
这是一个基于pnpm-workspace的Monorepo项目，采用微前端架构：

```bash
virtual-power-plant-web/
├── pnpm-workspace.yaml      # pnpm工作区配置文件
├── package.json             # 根目录的package.json文件
├── .npmrc                   # npm配置文件
├── plugins/                 # 插件目录
│   └── vpp-resource-manager/  # 资源管理插件
│       ├── src/             # 源代码目录
│       │   ├── main.js      # 插件入口文件
│       │   ├── omega/       # Omega框架配置
│       │   ├── router/      # 路由配置
│       │   ├── store/       # 状态管理目录
│       │   ├── projects/    # 业务模块目录
│       │   ├── api/         # 接口定义和地址常量
│       │   ├── config/      # 配置文件目录
│       │   ├── hooks/       # 自定义Hooks
│       │   ├── icons/       # SVG图标目录
│       │   └── resources/   # 样式资源目录
│       │       ├── index.css     # CSS样式入口
│       │       ├── index.scss    # SCSS样式入口
│       │       ├── var.scss      # SCSS变量文件
│       │       └── elementui/    # ElementUI定制样式
│       ├── public/          # 公共资源目录
│       │   ├── config.js    # 插件配置文件
│       │   └── index.html   # HTML模板
│       ├── package.json     # 插件配置文件
│       ├── vue.config.js    # Vue CLI配置文件
│       ├── tailwind.config.js # TailwindCSS配置文件
│       └── babel.config.js  # Babel配置文件
├── Fusion-template/         # 主应用模板
│   ├── main/               # 主应用
│   └── sub/                # 子应用模板
└── docs/                   # 项目文档
```

### 2. 技术栈
- **前端框架**: Vue 2.7.8
- **UI组件库**: ElementUI 2.15.6
- **CSS框架**: TailwindCSS 3.1.6
- **构建工具**: Vue CLI 5.0.8 + Webpack
- **包管理**: pnpm (Monorepo)
- **核心框架**: Omega框架系列
- **状态管理**: Vuex 3.5.1
- **路由管理**: Vue Router 3.5.3
- **工具库**: Lodash, Moment.js
- **图表库**: CET Chart
- **样式预处理**: SCSS

## 组件开发规范

### 1. Vue组件规范
- 组件文件名使用PascalCase命名法，如 `MyComponent.vue`
- projects目录下的业务模块使用小写字母和连字符，如 `my-module`
- 组件必须使用scoped样式避免全局污染
- 组件名称必须使用PascalCase，如 `MyComponent`

### 2. 插件开发标准

#### 插件目录结构规范
```
plugins/plugin-name/
├── src/
│   ├── main.js              # 插件入口文件
│   ├── omega/               # Omega框架配置
│   │   ├── index.js         # 插件注册入口
│   │   ├── http.js          # HTTP配置
│   │   ├── i18n.js          # 国际化配置
│   │   ├── theme.js         # 主题配置
│   │   └── subApp.js        # 子应用配置
│   ├── router/              # 路由配置
│   │   ├── index.js         # 路由定义
│   │   └── main.vue         # 路由容器组件
│   ├── projects/            # 业务模块
│   ├── api/                 # 接口定义
│   ├── config/              # 配置文件
│   ├── hooks/               # 自定义Hooks
│   ├── icons/               # SVG图标
│   └── resources/           # 样式资源
├── public/
│   └── config.js            # 插件配置
└── package.json
```

#### API接口定义规范
文件位置：`src/api/`
文件命名：接口URL最后一部分用小驼峰命名，如 `queryOrderStatus.ts`

```typescript
// 用户查询订单状态相关接口定义
// path: /api/v1/user/query_order_status
// desc: 用户查询订单状态

export const QUERY_ORDER_STATUS = '/api/v1/user/query_order_status'

// 请求参数接口
export interface IQueryOrderStatusReq {
  order_id: string; // 订单ID
}

// 响应数据接口
export interface IQueryOrderStatusResp {
  order_id: string; // 订单ID
  status: string; // 订单状态
}
```

#### 通信函数规范
文件位置：`src/api/custom.js`
使用项目已有的HTTP请求库和规范

```javascript
import { httping } from "@omega/http";
import { QUERY_ORDER_STATUS, type IQueryOrderStatusResp, type IQueryOrderStatusReq } from './queryOrderStatus'

// 查询订单状态
export const queryOrderStatus = (params: IQueryOrderStatusReq): Promise<IQueryOrderStatusResp> => {
  return httping.post(QUERY_ORDER_STATUS, params);
}
```

### 3. 样式开发规范

#### CSS变量使用规范
必须使用预定义的CSS变量，禁止硬编码颜色和尺寸：

```css
/* ✅ 正确：使用CSS变量 */
.component {
  background-color: var(--BG1);    /* 背景色 */
  color: var(--T1);                /* 主文本色 */
  font-size: var(--H3);            /* 标题字号 */
  padding: var(--J2);              /* 间距 */
  border-radius: var(--radius-md); /* 圆角 */
}

/* ❌ 错误：硬编码值 */
.component {
  background-color: #ffffff;
  color: #333333;
  font-size: 16px;
  padding: 16px;
}
```

#### 主题系统规范
所有样式必须支持明暗主题切换，在 `resources/_theme.extend.scss` 中定义主题变量：

```scss
$omega_theme_extend: (
  "light": (
    CUSTOM_BG: #f0f2f5,
    CUSTOM_COLOR: #1890ff
  ),
  "dark": (
    CUSTOM_BG: #141414,
    CUSTOM_COLOR: #177ddc
  )
);
```

#### TailwindCSS集成规范
使用TailwindCSS时必须配合CSS变量：

```vue
<template>
  <div class="bg-BG1 text-T1 p-J2 rounded-radius-md">
    <h3 class="text-H3 font-semibold mb-J1">标题</h3>
    <p class="text-T2">内容文本</p>
  </div>
</template>
```

### 4. 路由配置规范

```javascript
// router/index.js
export const appRoutes = [
  {
    path: "/",
    component: () => import("./main.vue"),
    children: [
      {
        path: "",
        redirect: "/dashboard"
      },
      {
        path: "/dashboard",
        component: () => import("@/projects/dashboard/index.vue"),
        meta: {
          keepAlive: true,    // 页面缓存
          title: "仪表盘"     // 页面标题
        }
      }
    ]
  }
];
```

### 5. Omega插件系统规范

#### 插件注册
```javascript
// src/omega/index.js
import omegaApp from "@omega/app";
import { OmegaHttpPlugin } from "@omega/http";
import { OmegaI18nPlugin } from "@omega/i18n/plugin";
import { OmegaThemePlugin } from "@omega/theme/plugin";
import { OmegaSubAppPlugin } from "@altair/knight/vue2";

// HTTP插件
omegaApp.plugin.register(OmegaHttpPlugin, {});

// 国际化插件
omegaApp.plugin.register(OmegaI18nPlugin, {
  defaultActive: "zh",
  en: require("../config/lang/en.json")
});

// 主题插件
omegaApp.plugin.register(OmegaThemePlugin, {
  defaultActive: "light",
  themeLimits: ["light", "dark"]
});

// 子应用插件
omegaApp.plugin.register(OmegaSubAppPlugin);
```

#### API调用规范
```javascript
import { api } from "@altair/knight";

// 路由跳转
api.subRouterQuery({
  path: "/target-page",
  query: { id: 123 }
});

// 获取路由参数
const { id } = api.getRouterQuery();
```

## 代码质量要求

### 1. 必须遵循的规范
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 组件必须使用scoped样式
- 必须使用CSS变量而非硬编码
- 支持明暗主题切换
- 遵循Vue官方风格指南

### 2. 禁止的做法
- ❌ 硬编码颜色值和尺寸
- ❌ 全局样式污染（不使用scoped）
- ❌ 内联样式
- ❌ 直接修改ElementUI源码
- ❌ 在组件中直接使用!important

### 3. 推荐的做法
- ✅ 使用CSS变量和预定义尺寸
- ✅ 使用scoped样式
- ✅ 使用TailwindCSS工具类
- ✅ 组件按需导入
- ✅ 路由懒加载
- ✅ 合理使用Vue组合式API

## 性能优化要求

### 1. 代码分割
```javascript
// 使用动态导入进行代码分割
const LazyComponent = () => import(
  /* webpackChunkName: "lazy-component" */
  "@/projects/module/LazyComponent.vue"
);
```

### 2. 组件优化
- 使用v-show替代v-if进行频繁切换
- 合理使用keep-alive缓存组件
- 避免在模板中使用复杂表达式
- 使用Object.freeze冻结大型数据对象

### 3. 样式优化
- 避免深层嵌套选择器
- 使用CSS变量减少重复代码
- 合理使用will-change属性

## 测试要求

### 1. 功能测试
- 每个新功能必须经过完整测试
- 确保在不同主题下正常显示
- 验证响应式布局在不同屏幕尺寸下的表现

### 2. 兼容性测试
- 支持主流现代浏览器
- 确保在主应用和独立运行模式下都能正常工作

## 文档要求

### 1. 代码注释
- 复杂逻辑必须添加注释
- API接口必须包含完整的类型定义和描述
- 组件props必须定义类型和默认值

### 2. 变更记录
- 重要变更必须更新CHANGELOG.md
- 新增功能需要更新相关文档

---

**注意**: 本规范基于项目当前的技术栈和架构设计，所有代码生成和建议都应严格遵循以上规范。
